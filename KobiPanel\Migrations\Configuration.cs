namespace KobiPanel.Migrations
{
    using KobiPanel.Models;
    using System;
    using System.Data.Entity;
    using System.Data.Entity.Migrations;
    using System.IO;
    using System.Linq;

    internal sealed class Configuration : DbMigrationsConfiguration<KobiPanel.Models.KobiPanelDbContext>
    {
        public Configuration()
        {
            AutomaticMigrationsEnabled = true;
            AutomaticMigrationDataLossAllowed = true;
        }

        protected override void Seed(KobiPanelDbContext context)
        {
            // Seed metodunu geçici olarak devre dışı bırak - validation hatalarını önlemek için
            return;
            context.Kullanicilar.Add(new Kullanicilar
            {
                KullaniciAdi = "ismail",
                AdSoyad = "İsmail Pehlevan",
                Sifre = "1jkdhdkj",
            });

            context.Kullanicilar.Add(new Kullanicilar
            {
                KullaniciAdi = "aleyna",
                AdSoyad = "Aleyna Simge Pehlevan",
                Sifre = "1jkdhdkj",
            });

            context.Kullanicilar.Add(new Kullanicilar
            {
                KullaniciAdi = "teslime",
                AdSoyad = "Teslime Pehlevan",
                Sifre = "4070",
            });

            context.Kullanicilar.Add(new Kullanicilar
            {
                KullaniciAdi = "ahmet",
                AdSoyad = "Ahmet Pehlevan",
                Sifre = "3246",
            });

            context.Kullanicilar.Add(new Kullanicilar
            {
                KullaniciAdi = "mustafa",
                AdSoyad = "Mustafa Pehlevan",
                Sifre = "4070",
            });

            context.Kullanicilar.Add(new Kullanicilar
            {
                KullaniciAdi = "arda",
                AdSoyad = "Arda Koca",
                Sifre = "123456",
            });

            //context.Urun.Add(new Urun
            //{
            //    UrunAdi = "B�y�k M�s�r Silaj�",
            //    EklenmeTarihi = DateTime.Now,
            //    UrunFiyati = 400,
            //    NakliyeUcreti = 30,
            //    MevcutStok = 1000,
            //    DuzenlenmeTarihi = DateTime.Now
            //}) ;

            //context.Urun.Add(new Urun
            //{
            //    UrunAdi = "K���k M�s�r Silaj�",
            //    EklenmeTarihi = DateTime.Now,
            //    UrunFiyati = 200,
            //    NakliyeUcreti = 20,
            //    MevcutStok = 1000,
            //    DuzenlenmeTarihi = DateTime.Now
            //});

            //context.Urun.Add(new Urun
            //{
            //    UrunAdi = "M�s�r Sap�",
            //    EklenmeTarihi = DateTime.Now,
            //    UrunFiyati = 250,
            //    NakliyeUcreti = 30,
            //    MevcutStok = 1000,
            //    DuzenlenmeTarihi = DateTime.Now
            //});



            if (context.City.Count() == 0)

            {

                var baseDir = AppDomain.CurrentDomain.BaseDirectory.Replace("\\bin", string.Empty) + "\\Migrations";

                context.Database.ExecuteSqlCommand(File.ReadAllText(baseDir + "\\il_ilce.sql"));
                context.Database.ExecuteSqlCommand(File.ReadAllText(baseDir + "\\29-02-2020-Musteri-Satis.sql"));

            }
            //if (context.Urun.Count() == 0)
            //{
            //    for (int i = 0; i < 4; i++)
            //    {
            //        context.Urun.Add(new Urun
            //        {
            //            UrunAdi = FakeData.TextData.GetSentence(),
            //            NakliyeUcreti = 10,
            //            DuzenlenmeTarihi = FakeData.DateTimeData.GetDatetime(),
            //            EklenmeTarihi = FakeData.DateTimeData.GetDatetime(),
            //            MevcutStok = 100,
            //            UrunFiyati = FakeData.NumberData.GetNumber(200, 420)
            //        }); ;
            //    }
            //    context.SaveChanges();


            //}

            //if (context.Musteriler.Count() == 0)
            //{
            //    for (int i = 0; i < 50; i++)
            //    {
            //        context.Musteriler.Add(new Musteriler
            //        {
            //            adsoyad = FakeData.NameData.GetFullName(),
            //            BuyukBasHayvanSayisi = FakeData.NumberData.GetNumber(1, 100),
            //            CityID = 11,
            //            kayitTarihi = FakeData.DateTimeData.GetDatetime(),
            //            DuzenlemeTarihi = FakeData.DateTimeData.GetDatetime(),
            //            KucukBasHayvanSayisi = FakeData.NumberData.GetNumber(1, 50),
            //            NeighborhoodID = 9937,
            //            TownID = 129,
            //            tel = FakeData.PhoneNumberData.GetPhoneNumber(),
            //            yas = short.Parse(FakeData.NumberData.GetNumber(20, 70).ToString())
            //        });
            //    }
            //    context.SaveChanges();
            //}



            //for (int i = 0; i < 100; i++)
            //{
            //    if (context.Satislar.Count() == 0)
            //    {
            //        context.Satislar.Add(new Satislar
            //        {
            //            MusteriID = FakeData.NumberData.GetNumber(1, 10),
            //            UrunID = FakeData.NumberData.GetNumber(1, 4),
            //            UrunAdeti = FakeData.NumberData.GetNumber(1, 10),
            //            KrediKartiMi = false,
            //            Tutar = FakeData.NumberData.GetNumber(200, 4200),
            //            TeslimEdildiMi = false,
            //            ServisMi = false,
            //            Aciklama = FakeData.TextData.GetSentence(),
            //            SatisTarihi = FakeData.DateTimeData.GetDatetime(),
            //            indirim = 0,
            //            NakliyeBedeli = 25,
            //            Musteriler = null,
            //            Urun = null
            //        });
        }
    }
}

